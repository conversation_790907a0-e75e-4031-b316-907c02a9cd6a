My background: 
I am researcher and postdoc. My domain in Hydrology. I am planning to build a simple website to easily find various data sources. We are not talking about data upload or data storage, but information about different data sources. As we read paper, we find different data sources, we want to store them here in a organized manner, so we can find them effectively later on.

Requirement:
Need to use free, no cost should be invlolved.
Need to use google site, google sheets (to store the data)
It should be nice looking ui/ux. Have basic feature like filtering and search.
Updating new or old data sources should be easy and should be possible by some form of authenticated users only. There will be a master google sheet, which will control the view of the site.
Use embed code feature to use custom html CSS.
Use appscript in google sheet to make api/api's and use them.
What we want (you are free to make it better): A single page (no tab or submenu) website. By default, It will have different category of data. Like Meteorological Data, Hydrological Data, Ecological Data Category as sections. Within them different data source (e.g. for meteorological data, PRISM, IMERG. Its like a tabular but not as table (will look bad), rows as different product, columns as feature of the data.
There should be a search bar at top, if search any keystroke, then matching data should be shown, if nothing there, all data should be shown.
Similarly a filtering dropdown option to view all or particular category of data.

Components:
1. Sample google sheet with 10 example dataset under 3 category and different info about them
2. Data API with Google Apps Script (save as code.gs).
3. HTML
3.1 Basic part
3.2 CSS Styling
3.3 JavaScript Logic

Tasks:
Each component will be one task.
Do one at a time. Don to two task together. Do one stop. When i say next task, then do the second one.


Do the first task: component 1.


Do the next task: component 2.
Do the next task: component 3.1
Do the next task: component 3.2
Do the next task: component 3.3


https://script.google.com/u/0/home/<USER>
AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw
https://script.google.com/macros/s/AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw/exec


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hydrology Data Source Finder</title>

	<style>
	    /* --- Basic Setup & Body --- */
	    body {
	        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
	        background-color: #f4f7f6;
	        color: #333;
	        margin: 0;
	        padding: 20px;
	    }

	    #app-container {
	        max-width: 1200px;
	        margin: auto;
	        padding: 10px;
	    }

	    /* --- Header & Controls --- */
	    .header {
	        text-align: center;
	        margin-bottom: 40px;
	    }

	    .header h1 {
	        font-size: 2.5rem;
	        color: #005A9C; /* A professional blue color */
	        margin-bottom: 10px;
	    }

	    .header p {
	        font-size: 1.1rem;
	        color: #555;
	    }

	    .controls-container {
	        display: flex;
	        justify-content: center;
	        gap: 20px;
	        margin-top: 30px;
	        flex-wrap: wrap; /* Allows controls to stack on smaller screens */
	    }

	    #search-input, #category-filter {
	        padding: 12px 15px;
	        border: 1px solid #ccc;
	        border-radius: 8px;
	        font-size: 1rem;
	        min-width: 280px;
	        transition: border-color 0.3s, box-shadow 0.3s;
	    }

	    #search-input:focus, #category-filter:focus {
	        outline: none;
	        border-color: #007BFF;
	        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
	    }
	    
	    /* --- Data Container & Cards --- */
	    #data-container {
	        display: grid;
	        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); /* Responsive grid */
	        gap: 25px;
	    }

	    .data-card {
	        background-color: #ffffff;
	        border-radius: 10px;
	        box-shadow: 0 4px 8px rgba(0,0,0,0.08);
	        padding: 25px;
	        display: flex;
	        flex-direction: column;
	        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	    }

	    .data-card:hover {
	        transform: translateY(-5px);
	        box-shadow: 0 8px 16px rgba(0,0,0,0.12);
	    }
	    
	    .card-header h2 {
	        margin: 0 0 5px 0;
	        color: #005A9C;
	        font-size: 1.5rem;
	    }

	    .card-header .category-tag {
	        font-size: 0.85rem;
	        background-color: #e9ecef;
	        color: #495057;
	        padding: 4px 8px;
	        border-radius: 5px;
	        display: inline-block;
	        margin-bottom: 15px;
	    }

	    .card-body p {
	        margin: 0 0 15px 0;
	        line-height: 1.6;
	    }
	    
	    .card-details {
	        margin-top: 15px;
	        font-size: 0.9rem;
	    }
	    
	    .card-details span {
	        display: block;
	        margin-bottom: 8px;
	    }
	    
	    .card-details strong {
	        color: #333;
	    }
	    
	    .card-footer {
	        margin-top: auto; /* Pushes the footer to the bottom of the card */
	        padding-top: 15px;
	        border-top: 1px solid #eee;
	    }

	    .card-footer a {
	        background-color: #007BFF;
	        color: white;
	        text-decoration: none;
	        padding: 10px 15px;
	        border-radius: 5px;
	        display: inline-block;
	        transition: background-color 0.2s;
	        text-align: center;
	        width: 100%;
	        box-sizing: border-box; /* Ensures padding doesn't affect width */
	    }

	    .card-footer a:hover {
	        background-color: #0056b3;
	    }
	    
	    /* --- Loader and Footer --- */
	    .loader {
	        text-align: center;
	        padding: 50px;
	        font-size: 1.2rem;
	        color: #777;
	    }
	    
	    footer {
	        text-align: center;
	        margin-top: 50px;
	        padding-top: 20px;
	        border-top: 1px solid #ddd;
	        color: #888;
	    }

	    /* --- Hide class for JS --- */
	    .hidden {
	        display: none !important;
	    }

	</style>

</head>

<body>

    <!-- Main application container that will wrap everything -->
    <div id="app-container">

        <!-- Header section for title and controls -->
        <header class="header">
            <h1>Hydrology Data Source Finder</h1>
            <p>A curated list of essential datasets for hydrological research.</p>
            
            <!-- Container for interactive controls like search and filter -->
            <div class="controls-container">
                <input type="search" id="search-input" placeholder="Search by name, keyword, etc...">
                <select id="category-filter">
                    <!-- Options will be populated by JavaScript -->
                    <option value="all">All Categories</option> 
                </select>
            </div>
        </header>

        <!-- Main content area where the data cards will be displayed -->
        <main id="data-container">
            <!-- This is where JavaScript will inject the data cards -->
        </main>

        <!-- A simple loader element to show while data is being fetched -->
        <div id="loader" class="loader">
            <p>Loading data from Google Sheet...</p>
        </div>

        <!-- Footer section -->
        <footer>
            <p>Managed by [Your Name/Lab Name] | Data stored in Google Sheets</p>
        </footer>

    </div>

    

	<script>
	    // --- CONFIGURATION ---
	    // IMPORTANT: Paste your Google Apps Script Web App URL here.
	    const API_URL = 'https://script.google.com/macros/s/AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw/exec'; 

	    // --- DOM ELEMENT REFERENCES ---
	    const dataContainer = document.getElementById('data-container');
	    const loader = document.getElementById('loader');
	    const searchInput = document.getElementById('search-input');
	    const categoryFilter = document.getElementById('category-filter');

	    // --- GLOBAL STATE ---
	    // This will hold the raw data from the sheet to avoid re-fetching
	    let allData = [];

	    // --- MAIN APPLICATION LOGIC ---

	    /**
	     * Main function to initialize the application.
	     * It's called once the DOM is fully loaded.
	     */
	    async function initializeApp() {
	        try {
	            // Show loader while we fetch data
	            loader.classList.remove('hidden');
	            
	            const response = await fetch(API_URL);
	            if (!response.ok) {
	                // Handle HTTP errors like 404 or 500
	                throw new Error(`Network response was not ok: ${response.statusText}`);
	            }
	            
	            allData = await response.json();
	            
	            // Once data is fetched, hide the loader
	            loader.classList.add('hidden');

	            // Use the data to build the page
	            populateCategoryFilter(allData);
	            renderDataCards(allData);

	        } catch (error) {
	            // If anything goes wrong, show an error message
	            loader.classList.add('hidden');
	            dataContainer.innerHTML = `<p style="color: red; text-align: center;">Error loading data. Please check the API URL and make sure the Google Sheet is shared correctly.</p>`;
	            console.error('Fetch error:', error);
	        }
	    }

	    /**
	     * Creates and injects the data cards into the DOM.
	     * @param {Array<Object>} data - An array of data source objects.
	     */
	    function renderDataCards(data) {
	        // If no data, show a message
	        if (!data || data.length === 0) {
	            dataContainer.innerHTML = '<p>No data sources found.</p>';
	            return;
	        }

	        // Generate an HTML string for each card and join them together
	        dataContainer.innerHTML = data.map(item => `
	            <div class="data-card" data-id="${item.ID}">
	                <div class="card-header">
	                    <h2>${item.Name}</h2>
	                    <span class="category-tag">${item.Category}</span>
	                </div>
	                <div class="card-body">
	                    <p>${item.Description}</p>
	                    <div class="card-details">
	                        <span><strong>Spatial Resolution:</strong> ${item.SpatialResolution}</span>
	                        <span><strong>Temporal Resolution:</strong> ${item.TemporalResolution}</span>
	                        <span><strong>Spatial Coverage:</strong> ${item.SpatialCoverage}</span>
	                        <span><strong>Temporal Coverage:</strong> ${item.TemporalCoverage}</span>
	                    </div>
	                </div>
	                <div class="card-footer">
	                    <a href="${item.Link}" target="_blank" rel="noopener noreferrer">Visit Source</a>
	                </div>
	            </div>
	        `).join('');
	    }

	    /**
	     * Populates the category filter dropdown with unique categories from the data.
	     * @param {Array<Object>} data - An array of data source objects.
	     */
	    function populateCategoryFilter(data) {
	        // Use a Set to automatically get unique category names
	        const categories = [...new Set(data.map(item => item.Category))];
	        
	        categories.sort().forEach(category => {
	            const option = document.createElement('option');
	            option.value = category;
	            option.textContent = category;
	            categoryFilter.appendChild(option);
	        });
	    }

	    /**
	     * Filters and searches the data cards based on user input.
	     */
	    function handleFilteringAndSearch() {
	        const searchTerm = searchInput.value.toLowerCase();
	        const selectedCategory = categoryFilter.value;

	        // Get all the cards that are currently in the DOM
	        const allCards = document.querySelectorAll('.data-card');

	        allCards.forEach(card => {
	            // Find the original data object that corresponds to this card
	            const cardData = allData.find(item => item.ID.toString() === card.dataset.id);
	            if (!cardData) return;

	            // Condition 1: Category match
	            const categoryMatch = selectedCategory === 'all' || cardData.Category === selectedCategory;

	            // Condition 2: Search term match
	            // We search in Name, Description, and Keywords for broader results
	            const searchableText = `
	                ${cardData.Name} 
	                ${cardData.Description} 
	                ${cardData.Keywords}
	            `.toLowerCase();
	            const searchMatch = searchableText.includes(searchTerm);

	            // If both conditions are met, show the card. Otherwise, hide it.
	            if (categoryMatch && searchMatch) {
	                card.classList.remove('hidden');
	            } else {
	                card.classList.add('hidden');
	            }
	        });
	    }


	    // --- EVENT LISTENERS ---

	    // Listen for the DOM to be fully loaded before running the app
	    document.addEventListener('DOMContentLoaded', initializeApp);

	    // Add listeners to the controls to trigger filtering/searching on any change
	    searchInput.addEventListener('input', handleFilteringAndSearch);
	    categoryFilter.addEventListener('change', handleFilteringAndSearch);

	</script>


</body>
</html>