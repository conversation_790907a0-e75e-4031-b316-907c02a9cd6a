# Google Apps Script Setup Guide

## Overview
This guide will help you set up the Google Apps Script API that serves your Google Sheets data as JSON for the website.

## Prerequisites
- Google account
- Google Sheet with "DataSources" tab created (from Task 1)
- Basic familiarity with Google Apps Script interface

## Step-by-Step Setup

### Step 1: Access Google Apps Script
1. Go to [script.google.com](https://script.google.com)
2. Click "New Project"
3. Rename the project to "Hydrology Data API" (click on "Untitled project")

### Step 2: Replace Default Code
1. Delete all existing code in the editor
2. Copy the entire contents of `code.gs` file
3. Paste it into the Apps Script editor
4. Save the project (Ctrl+S or Cmd+S)

### Step 3: Connect to Your Google Sheet
1. In the Apps Script editor, click on "Resources" or the "+" icon next to "Services"
2. If you don't see your spreadsheet automatically:
   - Click "Add a resource" → "Spreadsheet"
   - Enter your Google Sheet URL or ID
   - Click "Add"

### Step 4: Test the Script
1. In the Apps Script editor, select the `testApi` function from the dropdown
2. Click the "Run" button (▶️)
3. **First time only**: You'll need to authorize the script:
   - Click "Review permissions"
   - Choose your Google account
   - Click "Advanced" → "Go to [Project Name] (unsafe)"
   - Click "Allow"
4. Check the execution log for test results

### Step 5: Deploy as Web App
1. Click "Deploy" → "New deployment"
2. Click the gear icon ⚙️ next to "Type"
3. Select "Web app"
4. Configure deployment settings:
   - **Description**: "Hydrology Data API v1.0"
   - **Execute as**: "Me"
   - **Who has access**: "Anyone"
5. Click "Deploy"
6. **Important**: Copy the Web app URL - you'll need this for the website

### Step 6: Test the API Endpoint
1. Open the Web app URL in a new browser tab
2. You should see JSON data with your sheet contents
3. Verify the response structure matches expected format

## API Response Format

### Successful Response
```json
{
  "success": true,
  "message": "Successfully retrieved 10 records.",
  "timestamp": "2025-01-XX T12:00:00.000Z",
  "count": 10,
  "data": [
    {
      "ID": "1",
      "Name": "PRISM Climate Data",
      "Category": "Meteorological",
      "Description": "High-quality spatial climate data...",
      "SpatialResolution": "4km",
      "TemporalResolution": "Monthly",
      "SpatialCoverage": "Continental United States",
      "TemporalCoverage": "1895-present",
      "Keywords": "precipitation, temperature, climate",
      "Link": "https://prism.oregonstate.edu/",
      "_rowNumber": 2
    }
  ]
}
```

### Error Response
```json
{
  "success": false,
  "error": "Sheet 'DataSources' not found. Please ensure the sheet name matches exactly.",
  "timestamp": "2025-01-XX T12:00:00.000Z",
  "data": []
}
```

## Enhancements Made

### 1. **Improved Error Handling**
- Checks if sheet exists before processing
- Handles empty sheets gracefully
- Provides detailed error messages
- Logs errors for debugging

### 2. **Data Validation**
- Filters out completely empty rows
- Trims whitespace from all values
- Handles null/undefined values
- Adds row numbers for debugging

### 3. **Standardized Response Format**
- Consistent JSON structure
- Success/error status indicators
- Timestamps for debugging
- Record count information

### 4. **Testing Functions**
- `testApi()`: Test the main function locally
- `getSheetInfo()`: Debug sheet structure
- Detailed console logging

### 5. **Better Documentation**
- Comprehensive code comments
- Function parameter documentation
- Usage examples

## Troubleshooting

### Common Issues

**Issue**: "Sheet 'DataSources' not found"
- **Solution**: Ensure your sheet tab is named exactly "DataSources" (case-sensitive)

**Issue**: "Authorization required"
- **Solution**: Run the `testApi` function first to authorize the script

**Issue**: "Empty response or no data"
- **Solution**: 
  - Check that your sheet has data
  - Verify headers are in row 1
  - Run `getSheetInfo()` function to debug

**Issue**: "CORS errors in browser"
- **Solution**: Ensure deployment is set to "Anyone" access

### Testing Checklist
- [ ] Script runs without errors in Apps Script editor
- [ ] `testApi()` function returns expected data
- [ ] Web app URL returns JSON when accessed directly
- [ ] Response includes all expected fields
- [ ] Error handling works (test with wrong sheet name)

## Security Considerations

### Current Setup (Recommended for Public Data)
- **Execute as**: "Me" (script runs with your permissions)
- **Access**: "Anyone" (no authentication required)
- **Data**: Read-only access to your sheet

### For Sensitive Data (Alternative)
- Change access to "Anyone with Google account"
- Add authentication checks in the script
- Implement rate limiting if needed

## Updating the API

### To Update Data
- Simply edit your Google Sheet
- Changes are reflected immediately (no redeployment needed)

### To Update Code
1. Edit the script in Apps Script editor
2. Save the changes
3. Deploy → "Manage deployments"
4. Click pencil icon to edit existing deployment
5. Update version and click "Deploy"

## Your Current API URL
```
https://script.google.com/macros/s/AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw/exec
```

**Note**: This URL should work with the enhanced code. Test it after implementing the changes.
