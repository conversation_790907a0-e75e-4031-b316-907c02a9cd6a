/**
 * Hydrology Data Sources API - Google Apps Script
 * 
 * This script serves as a REST API endpoint to convert Google Sheets data
 * into JSON format for consumption by the frontend website.
 * 
 * <AUTHOR> Name
 * @version 1.1
 * @lastModified 2025-01-XX
 */

/**
 * @description
 * This function is the primary endpoint for our web app.
 * It is automatically triggered when a GET request is made to the deployed script URL.
 * It reads data from the 'DataSources' sheet and returns it as a JSON object.
 *
 * @param {Object} e - The event parameter for a web app, contains request information
 * @returns {ContentService.TextOutput} A JSON representation of the spreadsheet data
 */
function doGet(e) {
  try {
    // 1. Specify the name of the sheet where your data is stored.
    //    This MUST match the tab name in your Google Sheet exactly.
    const sheetName = 'DataSources';

    // 2. Open the active spreadsheet and get the specific sheet by its name.
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    
    // 3. Error handling: Check if sheet exists
    if (!sheet) {
      throw new Error(`Sheet '${sheetName}' not found. Please ensure the sheet name matches exactly.`);
    }

    // 4. Get all the data from the sheet.
    //    getDataRange() automatically selects all cells with content.
    //    getValues() returns a 2D array of the data.
    const data = sheet.getDataRange().getValues();
    
    // 5. Check if sheet has data
    if (data.length === 0) {
      return createJsonResponse([], 'No data found in the sheet.');
    }

    // 6. The first row of our data is the headers. We'll use these
    //    as keys in our JSON objects. .shift() removes the first
    //    element from the 'data' array and returns it.
    const headers = data.shift();

    // 7. Convert the array of arrays into an array of objects.
    //    This format is much easier to work with in JavaScript.
    const jsonData = data.map((row, index) => {
      const obj = {};
      headers.forEach((header, headerIndex) => {
        // For each header, create a property on the object with the
        // corresponding value from the current row.
        // We also trim whitespace from the header to create a clean JS key.
        const key = header.toString().trim();
        const value = row[headerIndex];
        
        // Handle different data types appropriately
        obj[key] = value !== null && value !== undefined ? value.toString().trim() : '';
      });
      
      // Add row number for debugging purposes (optional)
      obj._rowNumber = index + 2; // +2 because we removed headers and sheets are 1-indexed
      
      return obj;
    }).filter(item => {
      // Filter out completely empty rows
      return Object.values(item).some(value => value !== '' && value !== null && value !== undefined);
    });

    // 8. Return successful response
    return createJsonResponse(jsonData, `Successfully retrieved ${jsonData.length} records.`);
    
  } catch (error) {
    // 9. Error handling: Log error and return error response
    console.error('Error in doGet function:', error);
    return createErrorResponse(error.message);
  }
}

/**
 * Helper function to create a standardized JSON response
 * @param {Array} data - The data to return
 * @param {string} message - Success message
 * @returns {ContentService.TextOutput} Formatted JSON response
 */
function createJsonResponse(data, message = '') {
  const response = {
    success: true,
    message: message,
    timestamp: new Date().toISOString(),
    count: data.length,
    data: data
  };
  
  return ContentService
    .createTextOutput(JSON.stringify(response, null, 2))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Helper function to create a standardized error response
 * @param {string} errorMessage - The error message to return
 * @returns {ContentService.TextOutput} Formatted error response
 */
function createErrorResponse(errorMessage) {
  const response = {
    success: false,
    error: errorMessage,
    timestamp: new Date().toISOString(),
    data: []
  };
  
  return ContentService
    .createTextOutput(JSON.stringify(response, null, 2))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Test function to verify the API is working correctly
 * You can run this function manually in the Apps Script editor to test
 */
function testApi() {
  try {
    const result = doGet({});
    const response = JSON.parse(result.getContent());
    
    console.log('API Test Results:');
    console.log('Success:', response.success);
    console.log('Record Count:', response.count);
    console.log('First Record:', response.data[0]);
    
    return response;
  } catch (error) {
    console.error('Test failed:', error);
    return null;
  }
}

/**
 * Function to get sheet information for debugging
 */
function getSheetInfo() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sheets = spreadsheet.getSheets();
  
  console.log('Spreadsheet Name:', spreadsheet.getName());
  console.log('Available Sheets:');
  
  sheets.forEach(sheet => {
    console.log(`- ${sheet.getName()} (${sheet.getLastRow()} rows, ${sheet.getLastColumn()} columns)`);
  });
  
  return {
    spreadsheetName: spreadsheet.getName(),
    sheets: sheets.map(sheet => ({
      name: sheet.getName(),
      rows: sheet.getLastRow(),
      columns: sheet.getLastColumn()
    }))
  };
}
