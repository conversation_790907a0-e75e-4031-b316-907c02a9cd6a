# CSS Styling Guide

## Overview
This document explains the comprehensive CSS styling system created for the Hydrology Data Source Finder website, designed for modern browsers with accessibility and responsive design in mind.

## Design Philosophy

### 1. **Modern Glassmorphism Design**
- Semi-transparent backgrounds with backdrop blur effects
- Subtle shadows and gradients
- Clean, minimalist aesthetic suitable for academic/research use

### 2. **Accessibility First**
- High contrast ratios for readability
- Focus indicators for keyboard navigation
- Screen reader friendly design
- Reduced motion support for users with vestibular disorders

### 3. **Mobile-First Responsive**
- Designed for mobile devices first, then enhanced for larger screens
- Flexible grid systems and fluid typography
- Touch-friendly interface elements

## Color Palette

### Primary Colors
- **Primary Blue**: `#667eea` - Main brand color for buttons and accents
- **Secondary Purple**: `#764ba2` - Used in gradients and hover states
- **Dark Text**: `#2c3e50` - Primary text color
- **Medium Text**: `#5a6c7d` - Secondary text and descriptions
- **Light Text**: `#8a9ba8` - Placeholder text and subtle elements

### Background Colors
- **Main Background**: Linear gradient from `#f5f7fa` to `#c3cfe2`
- **Card Background**: `rgba(255, 255, 255, 0.95)` with backdrop blur
- **Error Background**: `#e74c3c` accent with white background

## Typography

### Font Stack
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
```

### Font Sizes (Responsive)
- **Main Title**: `clamp(2rem, 5vw, 3.5rem)` - Scales from 32px to 56px
- **Subtitle**: `clamp(1rem, 2.5vw, 1.3rem)` - Scales from 16px to 21px
- **Card Titles**: `22px` (desktop), `20px` (mobile)
- **Body Text**: `15-16px`
- **Small Text**: `12-14px`

## Layout System

### Container Structure
```css
#app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
}
```

### Grid System
- **Data Cards**: `repeat(auto-fill, minmax(380px, 1fr))`
- **Card Details**: `1fr 1fr` (2-column grid)
- **Mobile**: Single column layout

## Component Styles

### 1. Header Section
- **Glassmorphism effect** with `backdrop-filter: blur(10px)`
- **Gradient text** for the main title
- **Centered layout** with max-width constraint
- **Integrated search controls**

### 2. Search Controls
- **Rounded pill design** with `border-radius: 50px`
- **Focus states** with color and shadow transitions
- **Custom dropdown arrow** for select element
- **Responsive stacking** on mobile devices

### 3. Data Cards
- **Glassmorphism design** with subtle shadows
- **Gradient top border** for visual hierarchy
- **Hover animations** with `translateY(-8px)`
- **Flexible content areas** with proper spacing
- **Staggered animations** for visual appeal

### 4. Interactive Elements
- **Gradient buttons** with hover effects
- **Smooth transitions** (0.3s ease)
- **Box shadows** for depth perception
- **Transform effects** for user feedback

## Responsive Breakpoints

### Mobile (≤ 768px)
- Single column card layout
- Stacked search controls
- Reduced padding and margins
- Simplified card details grid

### Small Mobile (≤ 480px)
- Further reduced font sizes
- Minimal padding
- Optimized touch targets

### Desktop (> 768px)
- Multi-column card grid
- Side-by-side search controls
- Enhanced hover effects
- Larger typography

## Accessibility Features

### 1. **Focus Management**
```css
button:focus, input:focus, select:focus, a:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}
```

### 2. **High Contrast Support**
```css
@media (prefers-contrast: high) {
    .data-card { border: 2px solid #000; }
    .category-tag { background: #000 !important; }
}
```

### 3. **Reduced Motion Support**
```css
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        transition-duration: 0.01ms !important;
    }
}
```

### 4. **Screen Reader Support**
- `.visually-hidden` class for screen reader only content
- `.skip-link` for keyboard navigation
- Proper ARIA live regions styling

## Animation System

### 1. **Card Entrance Animation**
```css
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
```

### 2. **Staggered Delays**
- Cards animate in sequence with 0.1s delays
- Creates smooth, professional loading effect

### 3. **Hover Effects**
- Subtle `translateY(-8px)` on card hover
- Enhanced shadows for depth
- Button lift effects

## State Management Styles

### 1. **Loading States**
- Spinning animation for loader
- Centered layout with descriptive text
- Smooth opacity transitions

### 2. **Error States**
- Red accent border (`#e74c3c`)
- Clear typography hierarchy
- Prominent retry button

### 3. **Empty States**
- Centered messaging
- Clear call-to-action buttons
- Helpful user guidance

## Print Styles
```css
@media print {
    .header, .footer, .controls-container { display: none !important; }
    .data-card { break-inside: avoid; border: 1px solid #ddd; }
}
```

## Browser Compatibility

### Modern Browsers
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Fallbacks
- CSS Grid with flexbox fallback
- Backdrop-filter with solid background fallback
- Custom properties with static values

## Performance Optimizations

### 1. **Efficient Selectors**
- Minimal nesting depth
- Class-based selectors over complex hierarchies
- Optimized for rendering performance

### 2. **Hardware Acceleration**
- `transform` properties for animations
- `will-change` hints where appropriate
- GPU-accelerated effects

### 3. **Minimal Repaints**
- Transform-based animations
- Opacity transitions
- Efficient hover states

## Google Sites Integration

### Embedding Considerations
- All styles embedded in `<style>` tags
- No external dependencies
- Optimized for Google Sites container
- Responsive within iframe constraints

### Container Adjustments
- Flexible height adaptation
- Proper overflow handling
- Mobile-friendly within Google Sites

## Customization Guide

### Brand Colors
To customize for your institution:
1. Update gradient colors in `.main-title`
2. Modify button backgrounds
3. Adjust category tag colors
4. Update accent colors throughout

### Typography
To change fonts:
1. Update the font-family stack
2. Adjust font-size values
3. Modify line-height ratios

### Layout
To adjust spacing:
1. Modify container max-width
2. Adjust grid gap values
3. Update padding/margin values

## Future Enhancements
- Dark mode support
- Additional animation options
- Enhanced accessibility features
- Performance optimizations
