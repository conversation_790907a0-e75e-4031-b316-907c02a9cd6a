# HTML Structure Guide

## Overview
This document explains the HTML structure created for the Hydrology Data Source Finder website, designed for optimal integration with Google Sites.

## File Structure
- **`index.html`** - Complete HTML structure with embedded CSS and JavaScript placeholders

## HTML Architecture

### 1. Document Head
```html
<head>
    <!-- Essential meta tags for SEO and accessibility -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="...">
    
    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="...">
    
    <!-- Embedded CSS and JavaScript for Google Sites compatibility -->
</head>
```

**Key Features:**
- Responsive viewport configuration
- SEO-optimized meta descriptions
- Social media sharing support
- Embedded styles for Google Sites compatibility

### 2. Accessibility Features

#### Skip Navigation
```html
<a href="#main-content" class="skip-link">Skip to main content</a>
```

#### ARIA Labels and Roles
- `role="banner"` for header
- `role="main"` for main content
- `role="search"` for search controls
- `role="alert"` for error messages
- `aria-live="polite"` for dynamic content updates

#### Screen Reader Support
- Hidden labels for form inputs
- Descriptive help text
- Proper heading hierarchy (h1 → h2)

### 3. Main Structure Components

#### Header Section
```html
<header class="header" role="banner">
    <h1 class="main-title">Hydrology Data Source Finder</h1>
    <p class="subtitle">A curated collection...</p>
    
    <!-- Search and filter controls -->
    <div class="controls-container" role="search">
        <!-- Search input and category filter -->
    </div>
</header>
```

**Features:**
- Clear site title and description
- Integrated search and filter controls
- Results counter for user feedback

#### Main Content Area
```html
<main id="main-content" class="main-content" role="main">
    <!-- Loading indicator -->
    <div id="loader" class="loader">...</div>
    
    <!-- Error handling -->
    <div id="error-container" class="error-container hidden">...</div>
    
    <!-- Data display area -->
    <div id="data-container" class="data-container">
        <!-- Dynamic content inserted here -->
    </div>
    
    <!-- No results state -->
    <div id="no-results" class="no-results hidden">...</div>
</main>
```

**Features:**
- Loading states for better UX
- Error handling with retry functionality
- Empty state messaging
- Dynamic content area for data cards

#### Footer Section
```html
<footer class="footer" role="contentinfo">
    <p>Managed by [Your Research Lab]</p>
    <p>Last updated: <span id="last-updated">...</span></p>
    <p>
        <a href="#" id="about-link">About</a> |
        <a href="#" id="contact-link">Contact</a>
    </p>
</footer>
```

**Features:**
- Institution branding
- Last updated timestamp
- Contact and information links

### 4. Interactive Elements

#### Search Controls
```html
<input 
    type="search" 
    id="search-input" 
    placeholder="Search by name, description, or keywords..."
    aria-describedby="search-help"
    autocomplete="off"
>
```

#### Category Filter
```html
<select id="category-filter" aria-describedby="filter-help">
    <option value="all">All Categories</option>
    <!-- Dynamically populated -->
</select>
```

#### Action Buttons
- Retry button for error recovery
- Clear filters button for easy reset
- Modal close buttons

### 5. State Management Elements

#### Loading States
- Spinner animation placeholder
- Loading text with context
- ARIA live regions for screen readers

#### Error States
- Clear error messaging
- Retry functionality
- Helpful troubleshooting hints

#### Empty States
- No results found messaging
- Clear filter suggestions
- Helpful user guidance

### 6. Modal Structure (Optional)
```html
<div id="info-modal" class="modal hidden" role="dialog">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title">About This Project</h2>
            <button id="modal-close" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <!-- Modal content -->
        </div>
    </div>
</div>
```

## JavaScript Integration Points

### Global Configuration
```javascript
const API_CONFIG = {
    url: 'your-apps-script-url',
    timeout: 10000
};
```

### DOM Element References
```javascript
const elements = {
    loader: document.getElementById('loader'),
    dataContainer: document.getElementById('data-container'),
    searchInput: document.getElementById('search-input'),
    // ... other elements
};
```

### State Management Variables
```javascript
let allDataSources = [];
let filteredDataSources = [];
let currentCategory = 'all';
let currentSearchTerm = '';
```

## Google Sites Integration

### Embedding Instructions
1. **Copy the HTML content** from `<div id="app-container">` to `</div>`
2. **In Google Sites:**
   - Add an "Embed" component
   - Select "Embed code"
   - Paste the HTML content
   - Adjust height as needed (recommended: 800px minimum)

### CSS and JavaScript Embedding
- All CSS will be embedded in `<style>` tags within the HTML
- All JavaScript will be embedded in `<script>` tags within the HTML
- No external file dependencies for Google Sites compatibility

## Responsive Design Considerations

### Mobile-First Approach
- Flexible grid layouts
- Touch-friendly button sizes
- Readable font sizes on small screens

### Breakpoint Planning
- Mobile: < 768px
- Tablet: 768px - 1024px  
- Desktop: > 1024px

### Flexible Components
- Search and filter controls stack on mobile
- Data cards adapt to screen width
- Modal dialogs are mobile-friendly

## SEO and Performance

### SEO Optimization
- Semantic HTML5 elements
- Proper heading hierarchy
- Meta descriptions and keywords
- Open Graph tags for social sharing

### Performance Considerations
- Minimal external dependencies
- Efficient DOM manipulation points
- Lazy loading preparation for large datasets
- Optimized for Google Sites hosting

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- IE11+ support (if needed)
- Mobile browser optimization
- Progressive enhancement approach

## Next Steps
This HTML structure is ready for:
1. **Task 4**: CSS styling implementation
2. **Task 5**: JavaScript functionality
3. **Task 6**: Google Sites integration

The structure provides a solid foundation that's accessible, SEO-friendly, and optimized for the Google Sites platform.
