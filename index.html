<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A curated collection of hydrology, meteorological, and ecological data sources for researchers and scientists.">
    <meta name="keywords" content="hydrology, meteorological data, ecological data, research, datasets, PRISM, IMERG, USGS">
    <meta name="author" content="Hydrology Research Lab">
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="Hydrology Data Source Finder">
    <meta property="og:description" content="Find and explore essential datasets for hydrological research">
    <meta property="og:type" content="website">
    
    <title>Hydrology Data Source Finder</title>
    
    <!-- Favicon (optional - you can add your institution's favicon) -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS embedded for Google Sites compatibility -->
    <style>
        /* === CSS RESET AND BASE STYLES === */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8fafc;
            min-height: 100vh;
        }

        /* === ACCESSIBILITY === */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
            transition: top 0.3s;
        }

        .skip-link:focus {
            top: 6px;
        }

        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* === UTILITY CLASSES === */
        .hidden {
            display: none !important;
        }

        /* === MAIN CONTAINER === */
        #app-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* === HEADER STYLES === */
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: #ffffff;
            border-radius: 16px;
            padding: 40px 30px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
        }

        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .main-title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 15px;
            letter-spacing: -0.025em;
        }

        .subtitle {
            font-size: clamp(1rem, 2.5vw, 1.3rem);
            color: #64748b;
            margin-bottom: 30px;
            font-weight: 400;
        }

        /* === CONTROLS CONTAINER === */
        .controls-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
        }

        .search-wrapper,
        .filter-wrapper {
            position: relative;
        }

        #search-input,
        #category-filter {
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: #ffffff;
            transition: all 0.3s ease;
            min-width: 280px;
            outline: none;
        }

        #search-input:focus,
        #category-filter:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        #search-input::placeholder {
            color: #94a3b8;
        }

        #category-filter {
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 45px;
        }

        .results-info {
            background: #f1f5f9;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            color: #475569;
            font-weight: 500;
            border: 1px solid #e2e8f0;
        }

        /* === MAIN CONTENT === */
        .main-content {
            flex: 1;
            margin-bottom: 40px;
        }

        /* === LOADER STYLES === */
        .loader {
            text-align: center;
            padding: 60px 20px;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f1f5f9;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loader-text {
            font-size: 18px;
            color: #64748b;
            font-weight: 500;
        }

        /* === ERROR CONTAINER === */
        .error-container {
            text-align: center;
            padding: 60px 20px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid #fecaca;
            border-left: 4px solid #ef4444;
        }

        .error-container h2 {
            color: #dc2626;
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .error-container p {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 25px;
        }

        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .retry-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* === DATA CONTAINER === */
        .data-container {
            margin-top: 20px;
        }

        /* === CATEGORY SECTIONS === */
        .category-section {
            margin-bottom: 60px;
            background: #ffffff;
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
        }

        .category-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #1e40af, #7c3aed);
        }

        .category-section:last-child {
            margin-bottom: 0;
        }

        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
            position: relative;
        }

        .category-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #1e40af);
            border-radius: 1px;
        }

        .category-title {
            font-size: clamp(1.5rem, 3vw, 2rem);
            font-weight: 700;
            color: #1e40af;
            margin: 0;
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
        }

        .category-title::before {
            content: '';
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .category-count {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            color: #475569;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            margin-left: 15px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .category-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 30px;
        }

        /* === DATA CARDS === */
        .data-card {
            background: #ffffff;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #3b82f6;
        }

        .data-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border-color: #cbd5e1;
        }

        .card-header {
            margin-bottom: 20px;
        }

        .card-header h3 {
            font-size: 22px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.3;
        }

        .category-tag {
            display: inline-block;
            background: #1e40af;
            color: white;
            padding: 6px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .card-body {
            flex: 1;
            margin-bottom: 25px;
        }

        .card-body p {
            color: #64748b;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        /* === VARIABLES SECTION === */
        .card-variables {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
        }

        .card-variables strong {
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block;
            margin-bottom: 8px;
        }

        .variables-container {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .variables-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            overflow: hidden;
            transition: max-height 0.3s ease;
            align-items: flex-start;
            flex: 1;
            min-width: 0;
        }

        .variables-tags.collapsed {
            max-height: 32px; /* Approximately one row height */
        }

        .variables-tags.expanded {
            max-height: none;
        }

        .variable-tag {
            display: inline-block;
            background: #059669;
            color: white;
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: capitalize;
            white-space: nowrap;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .variable-tag:hover {
            background: #047857;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
        }

        .variable-tag.no-variables {
            background: #f1f5f9;
            color: #64748b;
            font-style: italic;
            border: 1px solid #e2e8f0;
        }

        .variable-tag.no-variables:hover {
            transform: none;
            box-shadow: none;
        }

        .variables-toggle {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            flex-shrink: 0;
            height: 32px;
            padding-top: 0;
        }

        .variables-toggle-btn {
            background: #e2e8f0;
            border: 1px solid #cbd5e1;
            color: #475569;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 10px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 2px;
            white-space: nowrap;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            min-width: 45px;
            justify-content: center;
        }

        .variables-toggle-btn:hover {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .variables-toggle-btn:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        .variables-toggle-icon {
            transition: transform 0.2s ease;
            font-size: 10px;
        }

        .variables-toggle-btn.expanded .variables-toggle-icon {
            transform: rotate(180deg);
        }

        .card-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            font-size: 14px;
        }

        .card-details span {
            display: flex;
            flex-direction: column;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #e2e8f0;
        }

        .card-details strong {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .card-details span:not(strong) {
            color: #64748b;
        }

        .card-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-footer a {
            display: inline-block;
            background: #3e84d4;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(74, 85, 104, 0.2);
            width: 100%;
            text-align: center;
        }

        .card-footer a:hover {
            background: #305580;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(74, 85, 104, 0.3);
        }

        /* === NO RESULTS === */
        .no-results {
            text-align: center;
            padding: 80px 20px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
        }

        .no-results h2 {
            color: #1e293b;
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .no-results p {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .clear-filters-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .clear-filters-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* === FOOTER === */
        .footer {
            margin-top: 60px;
            padding: 40px 30px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
            border: 1px solid #e2e8f0;
            text-align: center;
        }

        .footer-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .footer-text {
            font-size: 16px;
            color: #1e293b;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .footer-details {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 15px;
        }

        .footer-links {
            font-size: 14px;
        }

        .footer-links a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .footer-links a:hover {
            color: #2563eb;
            text-decoration: underline;
        }

        /* === MODAL === */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 25px 30px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #1e40af;
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 20px;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .modal-close:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-body p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .modal-body p:last-child {
            margin-bottom: 0;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 768px) {
            #app-container {
                padding: 15px;
            }

            .header {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .main-title {
                font-size: 2.2rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .controls-container {
                flex-direction: column;
                gap: 15px;
            }

            #search-input,
            #category-filter {
                min-width: 100%;
                width: 100%;
            }

            .category-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .category-section {
                margin-bottom: 40px;
                padding: 25px 20px;
            }

            .category-header {
                margin-bottom: 25px;
                padding-bottom: 15px;
            }

            .category-title {
                font-size: 1.4rem;
            }

            .category-title::before {
                width: 6px;
                height: 6px;
                margin-right: 10px;
            }

            .category-count {
                margin-left: 10px;
                padding: 6px 12px;
                font-size: 13px;
            }

            .data-card {
                padding: 25px 20px;
            }

            .card-variables {
                padding: 12px;
                margin-bottom: 15px;
            }

            .variables-tags {
                gap: 4px;
            }

            .variables-tags.collapsed {
                max-height: 26px; /* Smaller height for mobile */
            }

            .variables-toggle {
                height: 26px;
            }

            .variable-tag {
                font-size: 11px;
                padding: 3px 8px;
            }

            .variables-toggle-btn {
                font-size: 10px;
                padding: 2px 6px;
                gap: 2px;
            }

            .card-details {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .footer {
                padding: 30px 20px;
                margin-top: 40px;
            }

            .modal-content {
                width: 95%;
                margin: 20px;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-body {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 25px 15px;
            }

            .main-title {
                font-size: 1.8rem;
            }

            .data-card {
                padding: 20px 15px;
            }

            .card-header h3 {
                font-size: 20px;
            }

            .category-section {
                margin-bottom: 35px;
                padding: 20px 15px;
            }

            .category-header {
                margin-bottom: 20px;
                padding-bottom: 12px;
            }

            .category-title {
                font-size: 1.3rem;
            }

            .category-title::before {
                width: 5px;
                height: 5px;
                margin-right: 8px;
            }

            .category-count {
                margin-left: 8px;
                padding: 4px 10px;
                font-size: 12px;
            }

            .controls-container {
                gap: 12px;
            }

            #search-input,
            #category-filter {
                padding: 12px 15px;
                font-size: 15px;
            }

            .variables-tags.collapsed {
                max-height: 24px; /* Even smaller for very small screens */
            }

            .variables-toggle {
                height: 24px;
            }

            .variables-toggle-btn {
                font-size: 9px;
                padding: 2px 5px;
                gap: 1px;
                border-radius: 8px;
            }
        }

        /* === PRINT STYLES === */
        @media print {
            .header,
            .footer,
            .controls-container,
            .loader,
            .error-container,
            .no-results,
            .modal {
                display: none !important;
            }

            .data-card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ddd;
                margin-bottom: 20px;
            }

            .card-footer a {
                color: #333 !important;
                background: none !important;
                border: 1px solid #ddd;
            }
        }

        /* === ANIMATIONS === */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .data-card {
            animation: fadeIn 0.5s ease-out;
        }

        .data-card:nth-child(1) { animation-delay: 0.1s; }
        .data-card:nth-child(2) { animation-delay: 0.2s; }
        .data-card:nth-child(3) { animation-delay: 0.3s; }
        .data-card:nth-child(4) { animation-delay: 0.4s; }
        .data-card:nth-child(5) { animation-delay: 0.5s; }
        .data-card:nth-child(6) { animation-delay: 0.6s; }

        /* === FOCUS STYLES FOR ACCESSIBILITY === */
        button:focus,
        input:focus,
        select:focus,
        a:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* === HIGH CONTRAST MODE SUPPORT === */
        @media (prefers-contrast: high) {
            .data-card {
                border: 2px solid #000;
            }

            .category-tag {
                background: #000 !important;
                color: #fff !important;
            }

            .variable-tag {
                background: #000 !important;
                color: #fff !important;
            }

            .card-footer a {
                background: #000 !important;
                border: 2px solid #000;
            }
        }

        /* === REDUCED MOTION SUPPORT === */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .data-card:hover {
                transform: none;
            }

            .card-footer a:hover,
            .retry-button:hover,
            .clear-filters-button:hover {
                transform: none;
            }
        }
    </style>
</head>

<body>
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Main application container -->
    <div id="app-container">
        
        <!-- Header section with title and navigation controls -->
        <header class="header" role="banner">
            <div class="header-content">
                <h1 class="main-title">Hydrology Data Source Finder</h1>
                <p class="subtitle">A curated collection of essential datasets for hydrological research</p>
                
                <!-- Search and filter controls -->
                <div class="controls-container" role="search">
                    <div class="search-wrapper">
                        <label for="search-input" class="visually-hidden">Search data sources</label>
                        <input
                            type="search"
                            id="search-input"
                            placeholder="Search by name, variables, description, or keywords..."
                            aria-describedby="search-help"
                            autocomplete="off"
                        >
                        <div id="search-help" class="visually-hidden">
                            Search across data source names, variables, descriptions, and keywords
                        </div>
                    </div>
                    
                    <div class="filter-wrapper">
                        <label for="category-filter" class="visually-hidden">Filter by category</label>
                        <select id="category-filter" aria-describedby="filter-help">
                            <option value="all">All Categories</option>
                            <!-- Options will be populated dynamically by JavaScript -->
                        </select>
                        <div id="filter-help" class="visually-hidden">
                            Filter data sources by category type
                        </div>
                    </div>
                    
                    <!-- Results counter -->
                    <div class="results-info" aria-live="polite">
                        <span id="results-count">Loading...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main content area -->
        <main id="main-content" class="main-content" role="main">
            
            <!-- Loading indicator -->
            <div id="loader" class="loader" aria-live="polite">
                <div class="loader-spinner" aria-hidden="true"></div>
                <p class="loader-text">Loading data sources ...</p>
            </div>
            
            <!-- Error message container -->
            <div id="error-container" class="error-container hidden" role="alert">
                <h2>Unable to Load Data</h2>
                <p id="error-message">There was a problem loading the data sources. Please try refreshing the page.</p>
                <button id="retry-button" class="retry-button">Try Again</button>
            </div>
            
            <!-- Data container where cards will be displayed -->
            <div id="data-container" class="data-container">
                <!-- Data cards will be dynamically inserted here by JavaScript -->
            </div>
            
            <!-- No results message -->
            <div id="no-results" class="no-results hidden">
                <h2>No Data Sources Found</h2>
                <p>Try adjusting your search terms or category filter to find relevant data sources.</p>
                <button id="clear-filters" class="clear-filters-button">Clear All Filters</button>
            </div>
            
        </main>
        
        <!-- Footer section -->
        <footer class="footer" role="contentinfo">
            <div class="footer-content">
                <p class="footer-text">
                    <strong>Hydrology Data Source Finder</strong> | 
                    Managed by [Your Research Lab/Institution Name]
                </p>
                <p class="footer-details">
                    Data stored in Google Sheets | 
                    Last updated: <span id="last-updated">Loading...</span>
                </p>
                <p class="footer-links">
                    <a href="#" id="about-link">About This Project</a> | 
                    <a href="#" id="contact-link">Contact</a> | 
                    <a href="#" id="suggest-data-link">Suggest a Data Source</a>
                </p>
            </div>
        </footer>
        
    </div>
    
    <!-- Modal for additional information (optional) -->
    <div id="info-modal" class="modal hidden" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">About This Project</h2>
                <button id="modal-close" class="modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>This website catalogs essential data sources for hydrology research. The data is maintained in Google Sheets and updated regularly by our research team.</p>
                <p>If you have suggestions for additional data sources or notice any errors, please contact us.</p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript embedded for Google Sites compatibility -->
    <script>
        /**
         * Hydrology Data Source Finder - JavaScript Logic
         * Handles data fetching, search, filtering, and UI interactions
         */

        // === CONFIGURATION ===
        const API_CONFIG = {
            url: 'https://script.google.com/macros/s/AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw/exec',
            timeout: 10000, // 10 seconds
            retryAttempts: 3,
            retryDelay: 2000 // 2 seconds
        };

        // === GLOBAL STATE ===
        let allDataSources = [];
        let filteredDataSources = [];
        let currentCategory = 'all';
        let currentSearchTerm = '';
        let isLoading = false;
        let retryCount = 0;

        // === DOM ELEMENT REFERENCES ===
        const elements = {
            loader: document.getElementById('loader'),
            errorContainer: document.getElementById('error-container'),
            dataContainer: document.getElementById('data-container'),
            noResults: document.getElementById('no-results'),
            searchInput: document.getElementById('search-input'),
            categoryFilter: document.getElementById('category-filter'),
            resultsCount: document.getElementById('results-count'),
            retryButton: document.getElementById('retry-button'),
            clearFiltersButton: document.getElementById('clear-filters'),
            lastUpdated: document.getElementById('last-updated'),
            errorMessage: document.getElementById('error-message'),
            // Modal elements
            infoModal: document.getElementById('info-modal'),
            modalClose: document.getElementById('modal-close'),
            aboutLink: document.getElementById('about-link'),
            contactLink: document.getElementById('contact-link'),
            suggestDataLink: document.getElementById('suggest-data-link')
        };

        // === UTILITY FUNCTIONS ===

        /**
         * Debounce function to limit the rate of function calls
         * @param {Function} func - Function to debounce
         * @param {number} wait - Wait time in milliseconds
         * @returns {Function} Debounced function
         */
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        /**
         * Show/hide elements with proper accessibility
         * @param {HTMLElement} element - Element to show/hide
         * @param {boolean} show - Whether to show or hide
         */
        function toggleElement(element, show) {
            if (!element) return;

            if (show) {
                element.classList.remove('hidden');
                element.setAttribute('aria-hidden', 'false');
            } else {
                element.classList.add('hidden');
                element.setAttribute('aria-hidden', 'true');
            }
        }

        /**
         * Format date for display
         * @param {string} dateString - ISO date string
         * @returns {string} Formatted date
         */
        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return 'Unknown';
            }
        }

        /**
         * Sanitize HTML to prevent XSS attacks
         * @param {string} str - String to sanitize
         * @returns {string} Sanitized string
         */
        function sanitizeHTML(str) {
            if (!str) return '';
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }

        /**
         * Format variables into styled tags with collapsible functionality
         * @param {string} variables - Comma-separated variables string
         * @param {string} cardId - Unique identifier for the card
         * @returns {string} HTML string with variable tags and toggle functionality
         */
        function formatVariables(variables, cardId) {
            if (!variables || variables === 'Not specified') {
                return '<span class="variable-tag no-variables">Not specified</span>';
            }

            // Split by comma and clean up each variable
            const variableList = variables.split(',')
                .map(v => v.trim())
                .filter(v => v.length > 0);

            if (variableList.length === 0) {
                return '<span class="variable-tag no-variables">Not specified</span>';
            }

            // Create individual tags for each variable
            const variableTags = variableList
                .map(variable => `<span class="variable-tag">${sanitizeHTML(variable)}</span>`)
                .join('');

            // Always create the structure with toggle, but we'll determine visibility after rendering
            const containerId = `variables-${cardId}`;
            const toggleId = `toggle-${cardId}`;

            return `
                <div class="variables-container">
                    <div id="${containerId}" class="variables-tags collapsed" data-card-id="${cardId}">
                        ${variableTags}
                    </div>
                    <div class="variables-toggle" style="display: none;">
                        <button id="${toggleId}" class="variables-toggle-btn"
                                onclick="toggleVariables('${containerId}', '${toggleId}')"
                                aria-expanded="false"
                                aria-controls="${containerId}">
                            <span class="toggle-text">More</span>
                            <span class="variables-toggle-icon">▼</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // === DATA FETCHING ===

        /**
         * Fetch data from Google Apps Script API with retry logic
         * @returns {Promise<Object>} API response data
         */
        async function fetchDataSources() {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);

            try {
                const response = await fetch(API_CONFIG.url, {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // Handle the enhanced API response format
                if (data.success === false) {
                    throw new Error(data.error || 'API returned an error');
                }

                return data;
            } catch (error) {
                clearTimeout(timeoutId);

                if (error.name === 'AbortError') {
                    throw new Error('Request timed out. Please check your internet connection.');
                }

                throw error;
            }
        }

        /**
         * Load data with retry logic
         */
        async function loadDataSources() {
            if (isLoading) return;

            isLoading = true;
            showLoadingState();

            try {
                const response = await fetchDataSources();

                // Handle both old and new API response formats
                allDataSources = response.data || response;

                if (!Array.isArray(allDataSources) || allDataSources.length === 0) {
                    throw new Error('No data sources found in the response');
                }

                // Sort all data sources by name (case-insensitive) when first loaded
                allDataSources.sort((a, b) => {
                    const nameA = (a.Name || '').toLowerCase();
                    const nameB = (b.Name || '').toLowerCase();
                    return nameA.localeCompare(nameB);
                });

                // Update last updated timestamp
                if (response.timestamp && elements.lastUpdated) {
                    elements.lastUpdated.textContent = formatDate(response.timestamp);
                }

                // Initialize the UI
                populateCategoryFilter();
                applyFilters();
                hideLoadingState();
                retryCount = 0; // Reset retry count on success

            } catch (error) {
                console.error('Error loading data sources:', error);

                if (retryCount < API_CONFIG.retryAttempts) {
                    retryCount++;
                    console.log(`Retrying... Attempt ${retryCount}/${API_CONFIG.retryAttempts}`);

                    setTimeout(() => {
                        isLoading = false;
                        loadDataSources();
                    }, API_CONFIG.retryDelay);
                } else {
                    showErrorState(error.message);
                }
            } finally {
                if (retryCount >= API_CONFIG.retryAttempts || allDataSources.length > 0) {
                    isLoading = false;
                }
            }
        }

        // === UI STATE MANAGEMENT ===

        /**
         * Show loading state
         */
        function showLoadingState() {
            toggleElement(elements.loader, true);
            toggleElement(elements.errorContainer, false);
            toggleElement(elements.dataContainer, false);
            toggleElement(elements.noResults, false);

            if (elements.resultsCount) {
                elements.resultsCount.textContent = 'Loading...';
            }
        }

        /**
         * Hide loading state
         */
        function hideLoadingState() {
            toggleElement(elements.loader, false);
        }

        /**
         * Show error state
         * @param {string} message - Error message to display
         */
        function showErrorState(message) {
            hideLoadingState();
            toggleElement(elements.errorContainer, true);
            toggleElement(elements.dataContainer, false);
            toggleElement(elements.noResults, false);

            if (elements.errorMessage) {
                elements.errorMessage.textContent = message;
            }

            if (elements.resultsCount) {
                elements.resultsCount.textContent = 'Error loading data';
            }
        }

        /**
         * Show no results state
         */
        function showNoResultsState() {
            toggleElement(elements.loader, false);
            toggleElement(elements.errorContainer, false);
            toggleElement(elements.dataContainer, false);
            toggleElement(elements.noResults, true);

            if (elements.resultsCount) {
                elements.resultsCount.textContent = 'No results found';
            }
        }

        /**
         * Show data state
         */
        function showDataState() {
            toggleElement(elements.loader, false);
            toggleElement(elements.errorContainer, false);
            toggleElement(elements.dataContainer, true);
            toggleElement(elements.noResults, false);
        }

        // === CATEGORY FILTER MANAGEMENT ===

        /**
         * Populate the category filter dropdown with sorted categories
         */
        function populateCategoryFilter() {
            if (!elements.categoryFilter || !allDataSources.length) return;

            // Get unique categories and sort them alphabetically (case-insensitive)
            const categories = [...new Set(allDataSources.map(item => item.Category))]
                .filter(Boolean)
                .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

            // Clear existing options (except "All Categories")
            const allOption = elements.categoryFilter.querySelector('option[value="all"]');
            elements.categoryFilter.innerHTML = '';
            if (allOption) {
                elements.categoryFilter.appendChild(allOption);
            } else {
                const defaultOption = document.createElement('option');
                defaultOption.value = 'all';
                defaultOption.textContent = 'All Categories';
                elements.categoryFilter.appendChild(defaultOption);
            }

            // Add sorted category options
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                elements.categoryFilter.appendChild(option);
            });
        }

        // === FILTERING AND SEARCH ===

        /**
         * Filter data sources based on current search and category
         */
        function applyFilters() {
            if (!allDataSources.length) {
                showNoResultsState();
                return;
            }

            filteredDataSources = allDataSources.filter(item => {
                // Category filter
                const categoryMatch = currentCategory === 'all' ||
                                    (item.Category && item.Category.toLowerCase() === currentCategory.toLowerCase());

                // Search filter
                let searchMatch = true;
                if (currentSearchTerm.trim()) {
                    const searchTerm = currentSearchTerm.toLowerCase();
                    const searchableText = [
                        item.Name || '',
                        item.Description || '',
                        item.Keywords || '',
                        item.Variables || '',
                        item.SpatialCoverage || '',
                        item.TemporalCoverage || '',
                        item.SpatialResolution || '',
                        item.TemporalResolution || ''
                    ].join(' ').toLowerCase();

                    searchMatch = searchableText.includes(searchTerm);
                }

                return categoryMatch && searchMatch;
            });

            // Sort filtered results by name (case-insensitive)
            filteredDataSources.sort((a, b) => {
                const nameA = (a.Name || '').toLowerCase();
                const nameB = (b.Name || '').toLowerCase();
                return nameA.localeCompare(nameB);
            });

            // Update UI based on results
            if (filteredDataSources.length === 0) {
                showNoResultsState();
            } else {
                showDataState();
                renderDataCards();
            }

            updateResultsCount();
        }

        /**
         * Update the results count display
         */
        function updateResultsCount() {
            if (!elements.resultsCount) return;

            const total = allDataSources.length;
            const filtered = filteredDataSources.length;

            if (total === 0) {
                elements.resultsCount.textContent = 'No data sources available';
            } else if (currentSearchTerm.trim() || currentCategory !== 'all') {
                elements.resultsCount.textContent = `Showing ${filtered} of ${total} data sources`;
            } else {
                elements.resultsCount.textContent = `${total} data sources available`;
            }
        }

        /**
         * Handle search input changes
         */
        const handleSearch = debounce(() => {
            currentSearchTerm = elements.searchInput ? elements.searchInput.value.trim() : '';
            applyFilters();
        }, 300);

        /**
         * Handle category filter changes
         */
        function handleCategoryChange() {
            currentCategory = elements.categoryFilter ? elements.categoryFilter.value : 'all';
            applyFilters();
        }

        /**
         * Clear all filters
         */
        function clearAllFilters() {
            currentSearchTerm = '';
            currentCategory = 'all';

            if (elements.searchInput) {
                elements.searchInput.value = '';
            }

            if (elements.categoryFilter) {
                elements.categoryFilter.value = 'all';
            }

            applyFilters();
        }

        // === DATA RENDERING ===

        /**
         * Render data cards grouped by category with headers
         */
        function renderDataCards() {
            if (!elements.dataContainer) return;

            if (!filteredDataSources.length) {
                elements.dataContainer.innerHTML = '';
                return;
            }

            // Group data sources by category
            const groupedData = groupDataByCategory(filteredDataSources);

            // Generate HTML for each category section
            const sectionsHTML = Object.entries(groupedData).map(([category, items]) => {
                const categoryCardsHTML = items.map((item, index) => {
                    // Sanitize all data for security
                    const sanitizedItem = {
                        ID: sanitizeHTML(item.ID || ''),
                        Name: sanitizeHTML(item.Name || 'Unnamed Data Source'),
                        Category: sanitizeHTML(item.Category || 'Uncategorized'),
                        Description: sanitizeHTML(item.Description || 'No description available'),
                        SpatialResolution: sanitizeHTML(item.SpatialResolution || 'Not specified'),
                        TemporalResolution: sanitizeHTML(item.TemporalResolution || 'Not specified'),
                        SpatialCoverage: sanitizeHTML(item.SpatialCoverage || 'Not specified'),
                        TemporalCoverage: sanitizeHTML(item.TemporalCoverage || 'Not specified'),
                        Variables: sanitizeHTML(item.Variables || 'Not specified'),
                        Link: item.Link || '#'
                    };

                    return `
                        <div class="data-card" data-id="${sanitizedItem.ID}" style="animation-delay: ${index * 0.1}s">
                            <div class="card-header">
                                <h3>${sanitizedItem.Name}</h3>
                                <span class="category-tag">${sanitizedItem.Category}</span>
                            </div>
                            <div class="card-body">
                                <p>${sanitizedItem.Description}</p>
                                <div class="card-variables">
                                    <strong>Variables:</strong>
                                    ${formatVariables(sanitizedItem.Variables, sanitizedItem.ID)}
                                </div>
                                <div class="card-details">
                                    <span>
                                        <strong>Spatial Resolution</strong>
                                        ${sanitizedItem.SpatialResolution}
                                    </span>
                                    <span>
                                        <strong>Temporal Resolution</strong>
                                        ${sanitizedItem.TemporalResolution}
                                    </span>
                                    <span>
                                        <strong>Spatial Coverage</strong>
                                        ${sanitizedItem.SpatialCoverage}
                                    </span>
                                    <span>
                                        <strong>Temporal Coverage</strong>
                                        ${sanitizedItem.TemporalCoverage}
                                    </span>
                                </div>
                            </div>
                            <div class="card-footer">
                                <a href="${sanitizedItem.Link}"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   aria-label="Visit ${sanitizedItem.Name} data source">
                                    Visit Data Source
                                </a>
                            </div>
                        </div>
                    `;
                }).join('');

                return `
                    <section class="category-section" aria-labelledby="category-${category.toLowerCase().replace(/\s+/g, '-')}">
                        <div class="category-header">
                            <h2 id="category-${category.toLowerCase().replace(/\s+/g, '-')}" class="category-title">${sanitizeHTML(category)}</h2>
                            <span class="category-count">${items.length} ${items.length === 1 ? 'source' : 'sources'}</span>
                        </div>
                        <div class="category-cards">
                            ${categoryCardsHTML}
                        </div>
                    </section>
                `;
            }).join('');

            elements.dataContainer.innerHTML = sectionsHTML;

            // Setup variable toggles based on actual overflow
            setupVariableToggles();

            // Add click tracking for analytics (optional)
            trackCardViews();
        }

        /**
         * Group data sources by category and sort both categories and items
         * @param {Array} dataSources - Array of data source objects
         * @returns {Object} Object with categories as keys and arrays of sorted items as values
         */
        function groupDataByCategory(dataSources) {
            const grouped = {};

            dataSources.forEach(item => {
                const category = item.Category || 'Uncategorized';
                if (!grouped[category]) {
                    grouped[category] = [];
                }
                grouped[category].push(item);
            });

            // Sort categories alphabetically and sort items within each category by name
            const sortedCategories = Object.keys(grouped).sort();
            const sortedGrouped = {};
            sortedCategories.forEach(category => {
                // Sort items within each category by name (case-insensitive)
                sortedGrouped[category] = grouped[category].sort((a, b) => {
                    const nameA = (a.Name || '').toLowerCase();
                    const nameB = (b.Name || '').toLowerCase();
                    return nameA.localeCompare(nameB);
                });
            });

            return sortedGrouped;
        }

        /**
         * Track card views for analytics (placeholder)
         */
        function trackCardViews() {
            // This could be enhanced with Google Analytics or other tracking
            console.log(`Rendered ${filteredDataSources.length} data source cards`);
        }

        /**
         * Check if variables overflow and setup toggle buttons accordingly
         */
        function setupVariableToggles() {
            // Wait for DOM to be fully rendered
            setTimeout(() => {
                const variableContainers = document.querySelectorAll('.variables-tags[data-card-id]');

                variableContainers.forEach(container => {
                    const cardId = container.getAttribute('data-card-id');
                    const toggleContainer = container.parentElement.querySelector('.variables-toggle');
                    const toggleBtn = document.getElementById(`toggle-${cardId}`);

                    if (!toggleContainer || !toggleBtn) return;

                    // Temporarily expand to measure full height
                    const originalMaxHeight = container.style.maxHeight;
                    container.style.maxHeight = 'none';
                    const fullHeight = container.scrollHeight;

                    // Restore collapsed state
                    container.style.maxHeight = originalMaxHeight;
                    const collapsedHeight = parseInt(window.getComputedStyle(container).maxHeight);

                    // Check if content overflows
                    const needsToggle = fullHeight > collapsedHeight;

                    if (needsToggle) {
                        // Show toggle button
                        toggleContainer.style.display = 'flex';
                    } else {
                        // Hide toggle button
                        toggleContainer.style.display = 'none';
                        container.classList.remove('collapsed');
                        container.classList.add('expanded');
                    }
                });
            }, 100);
        }

        /**
         * Toggle variables visibility in a card
         * @param {string} containerId - ID of the variables container
         * @param {string} toggleId - ID of the toggle button
         */
        function toggleVariables(containerId, toggleId) {
            const container = document.getElementById(containerId);
            const toggleBtn = document.getElementById(toggleId);

            if (!container || !toggleBtn) return;

            const isExpanded = container.classList.contains('expanded');
            const toggleText = toggleBtn.querySelector('.toggle-text');
            const toggleIcon = toggleBtn.querySelector('.variables-toggle-icon');

            if (isExpanded) {
                // Collapse
                container.classList.remove('expanded');
                container.classList.add('collapsed');
                toggleBtn.classList.remove('expanded');
                toggleBtn.setAttribute('aria-expanded', 'false');
                if (toggleText) toggleText.textContent = 'More';
                if (toggleIcon) toggleIcon.textContent = '▼';
            } else {
                // Expand
                container.classList.remove('collapsed');
                container.classList.add('expanded');
                toggleBtn.classList.add('expanded');
                toggleBtn.setAttribute('aria-expanded', 'true');
                if (toggleText) toggleText.textContent = 'Less';
                if (toggleIcon) toggleIcon.textContent = '▲';
            }
        }

        // === MODAL MANAGEMENT ===

        /**
         * Show modal
         * @param {HTMLElement} modal - Modal element to show
         */
        function showModal(modal) {
            if (!modal) return;

            modal.classList.remove('hidden');
            modal.setAttribute('aria-hidden', 'false');

            // Focus management
            const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        /**
         * Hide modal
         * @param {HTMLElement} modal - Modal element to hide
         */
        function hideModal(modal) {
            if (!modal) return;

            modal.classList.add('hidden');
            modal.setAttribute('aria-hidden', 'true');

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // === EVENT HANDLERS ===

        /**
         * Handle retry button click
         */
        function handleRetry() {
            retryCount = 0;
            loadDataSources();
        }

        /**
         * Handle keyboard events for accessibility
         * @param {KeyboardEvent} event - Keyboard event
         */
        function handleKeyboardEvents(event) {
            // Escape key closes modals
            if (event.key === 'Escape' && elements.infoModal && !elements.infoModal.classList.contains('hidden')) {
                hideModal(elements.infoModal);
            }

            // Enter key on search input triggers search
            if (event.key === 'Enter' && event.target === elements.searchInput) {
                event.preventDefault();
                handleSearch();
            }
        }

        /**
         * Handle modal backdrop clicks
         * @param {MouseEvent} event - Click event
         */
        function handleModalBackdropClick(event) {
            if (event.target === elements.infoModal) {
                hideModal(elements.infoModal);
            }
        }

        // === INITIALIZATION ===

        /**
         * Initialize event listeners
         */
        function initializeEventListeners() {
            // Search input
            if (elements.searchInput) {
                elements.searchInput.addEventListener('input', handleSearch);
                elements.searchInput.addEventListener('keydown', handleKeyboardEvents);
            }

            // Category filter
            if (elements.categoryFilter) {
                elements.categoryFilter.addEventListener('change', handleCategoryChange);
            }

            // Retry button
            if (elements.retryButton) {
                elements.retryButton.addEventListener('click', handleRetry);
            }

            // Clear filters button
            if (elements.clearFiltersButton) {
                elements.clearFiltersButton.addEventListener('click', clearAllFilters);
            }

            // Modal controls
            if (elements.modalClose) {
                elements.modalClose.addEventListener('click', () => hideModal(elements.infoModal));
            }

            if (elements.aboutLink) {
                elements.aboutLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    showModal(elements.infoModal);
                });
            }

            if (elements.infoModal) {
                elements.infoModal.addEventListener('click', handleModalBackdropClick);
            }

            // Global keyboard events
            document.addEventListener('keydown', handleKeyboardEvents);

            // Window resize handler to recalculate variable toggles
            window.addEventListener('resize', debounce(setupVariableToggles, 250));

            // Contact and suggest data links (placeholder functionality)
            if (elements.contactLink) {
                elements.contactLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    alert('Contact functionality would be implemented here. Consider adding a contact form or email link.');
                });
            }

            if (elements.suggestDataLink) {
                elements.suggestDataLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    alert('Data suggestion functionality would be implemented here. Consider adding a form or Google Form link.');
                });
            }
        }

        /**
         * Initialize the application
         */
        function initializeApp() {
            try {
                // Initialize event listeners
                initializeEventListeners();

                // Load data sources
                loadDataSources();

                // Set initial last updated text
                if (elements.lastUpdated) {
                    elements.lastUpdated.textContent = 'Loading...';
                }

                console.log('Hydrology Data Source Finder initialized successfully');
            } catch (error) {
                console.error('Error initializing application:', error);
                showErrorState('Failed to initialize the application. Please refresh the page.');
            }
        }

        /**
         * Handle unhandled errors
         */
        function handleGlobalErrors() {
            window.addEventListener('error', (event) => {
                console.error('Global error:', event.error);
                if (!allDataSources.length && !isLoading) {
                    showErrorState('An unexpected error occurred. Please refresh the page.');
                }
            });

            window.addEventListener('unhandledrejection', (event) => {
                console.error('Unhandled promise rejection:', event.reason);
                if (!allDataSources.length && !isLoading) {
                    showErrorState('An unexpected error occurred. Please refresh the page.');
                }
            });
        }

        /**
         * Performance monitoring (optional)
         */
        function initializePerformanceMonitoring() {
            if ('performance' in window) {
                window.addEventListener('load', () => {
                    setTimeout(() => {
                        const perfData = performance.getEntriesByType('navigation')[0];
                        if (perfData) {
                            console.log(`Page load time: ${Math.round(perfData.loadEventEnd - perfData.loadEventStart)}ms`);
                        }
                    }, 0);
                });
            }
        }

        /**
         * Check if the browser supports required features
         */
        function checkBrowserSupport() {
            const requiredFeatures = [
                'fetch',
                'Promise',
                'addEventListener',
                'querySelector',
                'classList'
            ];

            const unsupportedFeatures = requiredFeatures.filter(feature => !(feature in window || feature in document.documentElement));

            if (unsupportedFeatures.length > 0) {
                console.warn('Unsupported browser features:', unsupportedFeatures);
                showErrorState('Your browser may not support all features of this website. Please consider updating your browser.');
                return false;
            }

            return true;
        }

        /**
         * Main initialization when DOM is ready
         */
        function onDOMContentLoaded() {
            // Check browser support
            if (!checkBrowserSupport()) {
                return;
            }

            // Initialize error handling
            handleGlobalErrors();

            // Initialize performance monitoring
            initializePerformanceMonitoring();

            // Initialize the main application
            initializeApp();
        }

        // === APPLICATION STARTUP ===

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', onDOMContentLoaded);
        } else {
            // DOM is already ready
            onDOMContentLoaded();
        }

        // === EXPORT FOR DEBUGGING (Development only) ===
        if (typeof window !== 'undefined') {
            window.HydrologyDataFinder = {
                // Expose functions for debugging
                loadDataSources,
                applyFilters,
                clearAllFilters,
                getAllDataSources: () => allDataSources,
                getFilteredDataSources: () => filteredDataSources,
                getCurrentFilters: () => ({ category: currentCategory, search: currentSearchTerm }),
                // Utility functions
                showModal,
                hideModal,
                // State
                isLoading: () => isLoading
            };

            // Make toggle function globally accessible for onclick handlers
            window.toggleVariables = toggleVariables;
        }

    </script>
</body>
</html>
