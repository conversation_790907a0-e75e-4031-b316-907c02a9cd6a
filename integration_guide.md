# Google Sites Integration Guide

## Overview
This guide provides step-by-step instructions for integrating your Hydrology Data Source Finder into Google Sites, including setup, testing, and troubleshooting.

## Prerequisites Checklist
Before starting integration, ensure you have completed:

- [ ] Google Sheet created with "DataSources" tab
- [ ] Sample data imported from `sample_data.csv`
- [ ] Google Apps Script deployed with enhanced `code.gs`
- [ ] Web app URL obtained and tested
- [ ] HTML, CSS, and JavaScript code ready from `index.html`

## Step-by-Step Integration

### Step 1: Create Google Site
1. Go to [sites.google.com](https://sites.google.com)
2. Click "Create" or "+" to start a new site
3. Choose "Blank" template
4. Name your site: "Hydrology Data Source Finder" (or your preferred name)
5. Choose a URL path (e.g., `hydrology-data-finder`)

### Step 2: Configure Site Settings
1. Click "Settings" (gear icon) in the top right
2. **General Settings:**
   - Site name: "Hydrology Data Source Finder"
   - Site description: "A curated collection of essential datasets for hydrological research"
3. **Sharing Settings:**
   - Set to "Anyone with the link can view" (or your preferred setting)
   - Consider "Public on the web" for maximum accessibility

### Step 3: Prepare the HTML Code
1. Open your `index.html` file
2. Copy everything **inside** the `<body>` tags (excluding `<body>` and `</body>`)
3. Start from: `<!-- Skip to main content link for accessibility -->`
4. End before: `</body>`

**Important**: You'll embed the content, not the full HTML document.

### Step 4: Add Embed Component
1. In Google Sites editor, click "Insert" → "Embed"
2. Select "Embed code"
3. Paste your copied HTML content
4. **Recommended settings:**
   - Width: 100%
   - Height: 1200px (adjust based on content)
5. Click "Insert"

### Step 5: Configure Page Layout
1. **Page Title**: Set to "Hydrology Data Source Finder"
2. **Remove default header** if you want the embedded content to be the main focus
3. **Adjust margins**: Use minimal margins for full-width display
4. **Mobile optimization**: Preview on mobile and adjust if needed

### Step 6: Test Basic Functionality
1. Click "Preview" to test your site
2. Verify the following:
   - [ ] Page loads without errors
   - [ ] Loading spinner appears initially
   - [ ] Data loads from Google Sheets
   - [ ] Search functionality works
   - [ ] Category filter works
   - [ ] Cards display properly
   - [ ] Links open in new tabs

## Advanced Configuration

### Custom Domain (Optional)
1. In Google Sites, go to Settings → Custom domains
2. Follow Google's instructions to connect your domain
3. Update any hardcoded URLs in your code

### Analytics Integration (Optional)
Add Google Analytics tracking:
1. In Google Sites, go to Settings → Analytics
2. Enter your Google Analytics tracking ID
3. Or add custom tracking code to your HTML

### SEO Optimization
1. **Page title**: Set descriptive title in Google Sites
2. **Meta description**: Add in site settings
3. **URL structure**: Use clean, descriptive URLs
4. **Header tags**: Ensure proper H1, H2 hierarchy in your HTML

## Testing Checklist

### Functionality Testing
- [ ] **Data Loading**
  - [ ] Initial data loads successfully
  - [ ] Loading spinner displays and disappears
  - [ ] Error handling works (test by temporarily breaking API URL)
  - [ ] Retry functionality works

- [ ] **Search Functionality**
  - [ ] Real-time search works as you type
  - [ ] Search across all fields (name, description, keywords)
  - [ ] Search is case-insensitive
  - [ ] Empty search shows all results
  - [ ] Special characters don't break search

- [ ] **Category Filtering**
  - [ ] Dropdown populates with categories from data
  - [ ] "All Categories" shows all data
  - [ ] Individual categories filter correctly
  - [ ] Category + search combination works

- [ ] **UI States**
  - [ ] Loading state displays properly
  - [ ] Error state shows with retry button
  - [ ] No results state appears when appropriate
  - [ ] Result count updates correctly

- [ ] **Data Cards**
  - [ ] All data fields display correctly
  - [ ] Links open in new tabs
  - [ ] Hover effects work
  - [ ] Cards are responsive

### Accessibility Testing
- [ ] **Keyboard Navigation**
  - [ ] Tab through all interactive elements
  - [ ] Enter key works on search
  - [ ] Escape key closes modals
  - [ ] Focus indicators are visible

- [ ] **Screen Reader Testing**
  - [ ] Use browser screen reader or NVDA/JAWS
  - [ ] All content is announced properly
  - [ ] ARIA labels work correctly
  - [ ] Live regions update properly

- [ ] **Visual Testing**
  - [ ] High contrast mode works
  - [ ] Text is readable at 200% zoom
  - [ ] Color contrast meets WCAG standards

### Responsive Testing
- [ ] **Mobile Devices**
  - [ ] iPhone (Safari)
  - [ ] Android (Chrome)
  - [ ] Tablet (iPad/Android)

- [ ] **Desktop Browsers**
  - [ ] Chrome (latest)
  - [ ] Firefox (latest)
  - [ ] Safari (latest)
  - [ ] Edge (latest)

- [ ] **Responsive Features**
  - [ ] Cards stack properly on mobile
  - [ ] Search controls stack on small screens
  - [ ] Text remains readable
  - [ ] Touch targets are adequate (44px minimum)

### Performance Testing
- [ ] **Load Times**
  - [ ] Initial page load < 3 seconds
  - [ ] Data loads within 5 seconds
  - [ ] Search responds quickly (< 300ms)

- [ ] **Network Conditions**
  - [ ] Works on slow 3G
  - [ ] Handles network interruptions
  - [ ] Retry logic functions properly

## Troubleshooting Common Issues

### Issue: "No data sources found"
**Possible Causes:**
- Google Apps Script not deployed correctly
- Sheet name doesn't match "DataSources"
- API URL is incorrect
- Permissions not set properly

**Solutions:**
1. Verify Google Apps Script deployment
2. Check sheet name exactly matches "DataSources"
3. Test API URL directly in browser
4. Ensure script permissions allow "Anyone" access

### Issue: "CORS errors in browser console"
**Cause:** Google Apps Script not configured for web access

**Solution:**
1. Redeploy Google Apps Script
2. Ensure "Execute as: Me" and "Who has access: Anyone"
3. Test API URL in incognito/private browser window

### Issue: "Search not working"
**Possible Causes:**
- JavaScript errors
- Event listeners not attached
- Data not loaded properly

**Solutions:**
1. Check browser console for errors
2. Verify DOM elements exist
3. Test with simple search terms first

### Issue: "Mobile layout broken"
**Possible Causes:**
- CSS not loading properly
- Viewport meta tag missing
- Google Sites container constraints

**Solutions:**
1. Verify CSS is embedded in HTML
2. Check Google Sites mobile preview
3. Adjust container height in embed settings

### Issue: "Slow loading"
**Possible Causes:**
- Large dataset
- Slow Google Apps Script response
- Network issues

**Solutions:**
1. Optimize Google Apps Script code
2. Implement data pagination if needed
3. Add loading progress indicators

## Maintenance and Updates

### Updating Data
1. **Add new data sources:**
   - Edit Google Sheet directly
   - Changes appear immediately (no redeployment needed)

2. **Modify existing data:**
   - Edit cells in Google Sheet
   - Refresh website to see changes

### Updating Code
1. **HTML/CSS/JavaScript changes:**
   - Edit the embedded code in Google Sites
   - Use "Preview" to test changes
   - Publish when satisfied

2. **Google Apps Script changes:**
   - Edit script in Apps Script editor
   - Save and redeploy if needed
   - Test API endpoint directly

### Backup Strategy
1. **Google Sheet:** Automatically backed up by Google
2. **Google Apps Script:** Download code as backup
3. **Google Sites:** Export site if needed
4. **HTML Code:** Keep local copies of all files

## Performance Optimization

### Google Sites Specific
1. **Minimize embed height:** Use exact height needed
2. **Optimize images:** Compress any images used
3. **Reduce external requests:** Keep everything embedded

### Code Optimization
1. **Minify CSS/JavaScript:** For production use
2. **Optimize animations:** Reduce for slower devices
3. **Implement lazy loading:** For large datasets

## Security Considerations

### Data Protection
- Ensure sensitive data isn't exposed in public sheets
- Use appropriate sharing settings
- Consider data anonymization if needed

### Code Security
- HTML sanitization is implemented
- External links use proper security attributes
- No sensitive information in client-side code

## Going Live

### Final Checklist
- [ ] All functionality tested and working
- [ ] Mobile responsiveness verified
- [ ] Accessibility requirements met
- [ ] Performance is acceptable
- [ ] Error handling works properly
- [ ] Content is accurate and up-to-date

### Launch Steps
1. Set Google Sites to public (if desired)
2. Share URL with intended users
3. Monitor for any issues
4. Gather user feedback
5. Plan future enhancements

### Post-Launch Monitoring
- Check Google Sites analytics
- Monitor Google Apps Script execution logs
- Gather user feedback
- Plan regular data updates
