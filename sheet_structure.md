# Google Sheet Structure Documentation

## Sheet Name
**DataSources** (must match exactly with the Google Apps Script code)

## Column Definitions

| Column Name | Data Type | Description | Example Values |
|-------------|-----------|-------------|----------------|
| **ID** | Integer | Unique identifier for each data source | 1, 2, 3, ... |
| **Name** | Text | Official name of the data source | "PRISM Climate Data", "GPM IMERG" |
| **Category** | Text | Data category (must be consistent) | "Meteorological", "Hydrological", "Ecological" |
| **Description** | Text | Brief description of the dataset and its purpose | "High-quality spatial climate data..." |
| **SpatialResolution** | Text | Spatial resolution of the data | "4km", "0.1°", "500m", "Point data" |
| **TemporalResolution** | Text | Temporal frequency of data collection | "Monthly", "Daily", "Hourly", "16-day" |
| **SpatialCoverage** | Text | Geographic extent of the dataset | "Global", "Continental United States", "CONUS" |
| **TemporalCoverage** | Text | Time period covered by the dataset | "1895-present", "2000-2017" |
| **Variables** | Text | Comma-separated list of measured variables | "Temperature, Precipitation", "NDVI, EVI" |
| **Keywords** | Text | Comma-separated searchable terms | "precipitation, temperature, climate" |
| **Link** | URL | Direct link to the data source or main website | "https://prism.oregonstate.edu/" |

## Data Categories

### 1. Meteorological Data
Weather and climate-related datasets including:
- Precipitation data
- Temperature records
- Atmospheric reanalysis
- Climate projections

### 2. Hydrological Data
Water-related datasets including:
- Streamflow measurements
- Groundwater data
- Snow cover information
- Water quality data

### 3. Ecological Data
Environmental and biological datasets including:
- Vegetation indices
- Species occurrence data
- Forest cover data
- Biodiversity information

## Setup Instructions

### Step 1: Create Google Sheet
1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Hydrology Data Sources" (or your preferred name)
4. Rename the first sheet tab to "DataSources" (exactly as shown)

### Step 2: Add Headers
1. In row 1, add the following headers in order:
   - A1: ID
   - B1: Name
   - C1: Category
   - D1: Description
   - E1: SpatialResolution
   - F1: TemporalResolution
   - G1: SpatialCoverage
   - H1: TemporalCoverage
   - I1: Variables
   - J1: Keywords
   - K1: Link

### Step 3: Import Sample Data
1. Open the `sample_data.csv` file
2. Copy all content (including headers)
3. Paste into your Google Sheet starting at cell A1
4. Verify data appears correctly in all columns

### Step 4: Format the Sheet
1. **Bold the header row** (row 1)
2. **Freeze the header row**: View → Freeze → 1 row
3. **Auto-resize columns**: Select all → Format → Column → Resize columns A-K
4. **Wrap text in Description and Variables columns**: Select columns D and I → Format → Wrapping → Wrap

### Step 5: Set Permissions
1. Click "Share" button in top-right
2. Change access to "Anyone with the link can view"
3. Copy the sharing link for later use

## Data Entry Guidelines

### Adding New Data Sources
1. Always increment the ID number
2. Use consistent category names (exactly: "Meteorological", "Hydrological", "Ecological")
3. Keep descriptions concise but informative (1-2 sentences)
4. Use standard units for spatial resolution (km, m, degrees)
5. Be consistent with temporal resolution terms
6. **Variables column guidelines:**
   - List primary measured variables separated by commas
   - Use standard scientific terminology (e.g., "Temperature", "Precipitation", "NDVI")
   - Include 1-6 most important variables (avoid overwhelming users)
   - Use title case for consistency (e.g., "Soil Moisture" not "soil moisture")
7. Include relevant keywords separated by commas
8. Verify all links are working and accessible

### Data Quality Checks
- [ ] All required fields are filled
- [ ] Category names match exactly
- [ ] Links are functional
- [ ] No duplicate IDs
- [ ] Keywords are relevant and searchable
- [ ] Descriptions are clear and concise

## Integration with Apps Script
The Google Apps Script (`code.gs`) will:
1. Read data from the "DataSources" sheet
2. Convert it to JSON format
3. Serve it via a web API
4. Handle CORS for web access

**Important**: The sheet name "DataSources" must match exactly with the `sheetName` variable in the Apps Script code.

## Sample Data Overview
The provided sample includes:
- **4 Meteorological datasets**: PRISM, IMERG, ERA5, CHELSA
- **3 Hydrological datasets**: USGS Water Data, GRACE, MODIS Snow
- **3 Ecological datasets**: MODIS NDVI, Global Forest Watch, eBird

This provides a good foundation for testing and demonstrates the variety of data sources commonly used in hydrology research.
